#!/usr/bin/env python3
"""
测试API兼容性
测试wait_time不填写字段情况，以及task_aggregation_step存在或不存在的情况
"""

import json
import requests
import uuid
from typing import Dict, Any


def test_api_compatibility():
    """测试API兼容性"""
    base_url = "http://localhost:8888"
    
    # 测试数据1：你提供的原始数据（包含task_aggregation_step和部分缺少wait_time）
    test_data_1 = {
        "task_id": str(uuid.uuid4()),
        "task_name": "热门玩法筛选功能",
        "task_expect_result": {
            "text": "选择指定的玩法"
        },
        "task_step_by_step": [
            {
                "step": "1.点击页面上方'首页'分类",
                "expect_result": {
                    "wait_time": 1.5
                }
            },
            {
                "step": "2.在二级导航栏中滑动找到'王者'标签，二级导航栏在功能模型块下方",
                "expect_result": {}
            },
            {
                "step": "3.选中'王者'",
                "expect_result": {}
            },
            {
                "step": "4.点击【王者】下方的右侧的带图标'筛选'按钮",
                "expect_result": {}
            },
            {
                "step": "5.选中'热门玩法'的'找女生'选项",
                "expect_result": {}
            },
            {
                "step": "6.向上拖动寻找'分路'分类",
                "expect_result": {}
            },
            {
                "step": "7.在'分路'下方右侧点击'展开'按钮，展开更多信息",
                "expect_result": {}
            },
            {
                "step": "8.向上滑动寻找'打野'，选中'打野'",
                "expect_result": {}
            },
            {
                "step": "9.点击确定",
                "expect_result": {}
            }
        ],
        "task_aggregation_step": "",
        "app_id": "com.yiyou.ga",
        "agent_type": "android",
        "agent_config_id": "tt",
        "device": {
            "type": "android",
            "android": {
                "url": "************"
            }
        }
    }
    
    # 测试数据2：没有task_aggregation_step字段
    test_data_2 = {
        "task_id": str(uuid.uuid4()),
        "task_name": "测试没有task_aggregation_step字段",
        "task_expect_result": {
            "text": "测试结果"
        },
        "task_step_by_step": [
            {
                "step": "测试步骤1",
                "expect_result": {
                    "wait_time": 2.0
                }
            },
            {
                "step": "测试步骤2",
                "expect_result": {}
            }
        ],
        "execution_mode": "step_by_step",
        "app_id": "com.test.app",
        "agent_type": "android",
        "agent_config_id": "tt",
        "device": {
            "type": "android",
            "android": {
                "url": "127.0.0.1:5555"
            }
        }
    }
    
    # 测试数据3：有task_aggregation_step但没有task_step_by_step
    test_data_3 = {
        "task_id": str(uuid.uuid4()),
        "task_name": "测试旧版本兼容性",
        "task_expect_result": {
            "text": "测试结果"
        },
        "task_aggregation_step": "1.点击登录按钮 2.输入用户名和密码 3.点击登录",
        "app_id": "com.test.app",
        "agent_type": "android",
        "agent_config_id": "tt",
        "device": {
            "type": "android",
            "android": {
                "url": "127.0.0.1:5555"
            }
        }
    }
    
    test_cases = [
        ("原始数据（包含task_aggregation_step和部分缺少wait_time）", test_data_1),
        ("没有task_aggregation_step字段", test_data_2),
        ("有task_aggregation_step但没有task_step_by_step", test_data_3)
    ]
    
    print("🚀 开始测试API兼容性\n")
    
    results = []
    
    for test_name, test_data in test_cases:
        print(f"🧪 测试: {test_name}")
        print(f"📝 请求数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        try:
            # 发送POST请求到创建任务接口
            response = requests.post(
                f"{base_url}/api/v1/ui-task/create",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ 请求成功")
                print(f"📄 响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                results.append(f"{test_name}: ✅ 成功")
            else:
                print(f"❌ 请求失败")
                print(f"📄 错误响应: {response.text}")
                results.append(f"{test_name}: ❌ 失败 (状态码: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            results.append(f"{test_name}: ❌ 网络异常")
        except Exception as e:
            print(f"❌ 其他异常: {e}")
            results.append(f"{test_name}: ❌ 其他异常")
        
        print("-" * 80)
    
    # 输出测试结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    for result in results:
        print(f"  {result}")
    
    # 统计成功率
    success_count = sum(1 for result in results if "✅" in result)
    total_count = len(results)
    success_rate = (success_count / total_count) * 100
    
    print(f"\n🎯 测试成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有兼容性测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查兼容性处理")


def test_direct_validation():
    """直接测试数据验证逻辑"""
    print("\n🔍 直接测试数据验证逻辑")
    
    # 导入必要的类
    import sys
    sys.path.append('/Users/<USER>/Workspace/click-pilot')
    
    try:
        from src.api.v1.dto import UITaskCreateRequest, TestCaseStep, StepExpectedResult, DeviceConfig, AndroidDeviceConfig
        
        # 测试1：wait_time为None的情况
        print("\n📝 测试1: wait_time为None的情况")
        step_with_none_wait_time = TestCaseStep(
            step="测试步骤",
            expect_result=StepExpectedResult(wait_time=None)
        )
        print(f"✅ wait_time=None 处理成功: {step_with_none_wait_time.expect_result.wait_time}")
        
        # 测试2：expect_result为空字典的情况
        print("\n📝 测试2: expect_result为空字典的情况")
        step_with_empty_result = TestCaseStep(
            step="测试步骤",
            expect_result=StepExpectedResult()
        )
        print(f"✅ 空expect_result处理成功: wait_time={step_with_empty_result.expect_result.wait_time}")
        
        # 测试3：完整的请求验证
        print("\n📝 测试3: 完整的请求验证")
        device_config = DeviceConfig(
            type="android",
            android=AndroidDeviceConfig(url="************")
        )
        
        request = UITaskCreateRequest(
            task_id="test-123",
            task_name="兼容性测试",
            task_step_by_step=[
                TestCaseStep(
                    step="步骤1",
                    expect_result=StepExpectedResult(wait_time=1.5)
                ),
                TestCaseStep(
                    step="步骤2",
                    expect_result=StepExpectedResult()  # 使用默认wait_time
                )
            ],
            task_aggregation_step="",  # 空字符串
            app_id="com.test.app",
            device=device_config
        )
        
        print(f"✅ 完整请求验证成功")
        print(f"   - execution_mode: {request.execution_mode}")
        print(f"   - 步骤数量: {len(request.task_step_by_step)}")
        for i, step in enumerate(request.task_step_by_step, 1):
            wait_time = step.expect_result.wait_time if step.expect_result else "None"
            print(f"   - 步骤{i}: {step.step}, wait_time: {wait_time}")
        
    except Exception as e:
        print(f"❌ 直接验证失败: {e}")


if __name__ == "__main__":
    # 先测试直接验证逻辑
    test_direct_validation()
    
    # 再测试API接口
    test_api_compatibility()
